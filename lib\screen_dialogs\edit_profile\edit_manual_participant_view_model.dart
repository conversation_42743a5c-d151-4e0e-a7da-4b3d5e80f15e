import 'package:image_picker/image_picker.dart';
import 'package:venvi/constants/image_type.dart';
import 'package:venvi/custom_classes/con_data.dart';
import 'package:venvi/models/image_model.dart';
import 'package:venvi/models/participant_model.dart';
import 'package:venvi/repositories/image_repository.dart';
import 'package:venvi/repositories/participant_repository.dart';
import 'package:venvi/screen_dialogs/edit_profile/edit_manual_participant_state.dart';
import 'package:venvi/widgets/editor_scaffold_exception.dart';
import 'package:venvi/widgets/editor_scaffold_view_model.dart';

class EditManualParticipantViewModel extends EditorScaffoldViewModel<EditManualParticipantState> {
  final ConData conData;
  final ParticipantRepository _participantRepository;
  final ImageRepository _imageRepository;
  final String? participantId;
  final bool isNewParticipant;

  EditManualParticipantViewModel(
    ParticipantModel initialParticipant,
    this.conData,
    this._participantRepository,
    this._imageRepository,
    this.participantId,
    this.isNewParticipant,
  ) : super(EditManualParticipantState(participantModel: initialParticipant));

  @override
  bool checkChanges(EditManualParticipantState initialState, EditManualParticipantState currentState) {
    return currentState.participantModel != initialState.participantModel ||
        currentState.profilePhotoOverrideFile != initialState.profilePhotoOverrideFile;
  }

  @override
  Future<EditManualParticipantState> applyChanges(
    EditManualParticipantState initialState,
    EditManualParticipantState state,
  ) async {
    final errors = <String>[];

    if (state.participantModel.displayName?.isNotEmpty != true) {
      errors.add('Display name is required');
    }

    if (errors.isNotEmpty) {
      throw EditorScaffoldException(errors);
    }

    ParticipantModel model = state.participantModel;

    // Creates image model for manual participant photo
    late final ImageModel? profilePhotoUploadModel;
    if (state.profilePhotoOverrideFile != null) {
      profilePhotoUploadModel = _imageRepository.createImageModel(conData, ImageType.manualParticipantPhoto);
      model = model.copyWith(customPhotoData: profilePhotoUploadModel, photoUrl: profilePhotoUploadModel.downloadUrl);
    } else {
      profilePhotoUploadModel = null;
    }

    if (isNewParticipant) {
      final docId = await _participantRepository.createManualParticipant(conData, model);
      if (docId != null) {
        model = model.copyWith(id: docId);
        // Upload profile photo after participant is created
        if (profilePhotoUploadModel != null && state.profilePhotoOverrideFile != null) {
          await _imageRepository.uploadImage(profilePhotoUploadModel, state.profilePhotoOverrideFile!);
        }
        return state.copyWith(participantModel: model);
      } else {
        throw const EditorScaffoldException(['Failed to create profile']);
      }
    } else {
      final success = await _participantRepository.updateManualParticipant(conData, participantId!, model);
      if (success) {
        // Upload profile photo after participant is updated
        if (profilePhotoUploadModel != null && state.profilePhotoOverrideFile != null) {
          await _imageRepository.uploadImage(profilePhotoUploadModel, state.profilePhotoOverrideFile!);
        }
        return state.copyWith(participantModel: model);
      } else {
        throw const EditorScaffoldException(['Failed to update profile']);
      }
    }
  }

  void setDisplayName(String displayName) {
    emit(state.copyWith(participantModel: state.participantModel.copyWith(displayName: displayName.trim())));
  }

  void setConHeadline(String conHeadline) {
    emit(state.copyWith(participantModel: state.participantModel.copyWith(conHeadline: conHeadline.trim())));
  }

  void setConBio(String conBio) {
    emit(state.copyWith(participantModel: state.participantModel.copyWith(conBio: conBio.trim())));
  }

  void setProfilePhotoFile(XFile image) {
    final newState = state.copyWith(
      participantModel: state.participantModel.copyWith(customPhotoData: null),
      profilePhotoOverrideFile: image,
    );
    emit(newState);
  }

  void removeProfilePhoto() {
    final newState = state.copyWith(
      participantModel: state.participantModel.copyWith(
        customPhotoData: null,
        photoUrl: state.participantModel.authPhotoUrl,
      ),
      profilePhotoOverrideFile: null,
    );
    emit(newState);
  }
}
