import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:venvi/constants/social_media.dart';
import 'package:venvi/json_converters/timestamp_converter.dart';
import 'package:venvi/models/base_profile.dart';
import 'package:venvi/models/image_model.dart';
import 'package:venvi/models/participant_model.dart';

part 'profile_model.freezed.dart';
part 'profile_model.g.dart';

@freezed
sealed class ProfileModel with _$ProfileModel implements BaseProfile {
  const factory ProfileModel({
    String? id,
    String? username,
    String? displayName,
    String? searchUsername,
    String? searchDisplayName,
    String? photoUrl,
    String? authPhotoUrl,
    ImageModel? customPhoto,
    String? headline,
    String? bio,
    Map<SocialMedia, String>? socialMediaLinks,
    @TimestampConverter() Timestamp? lastUpdatedUsername,
  }) = _ProfileModel;

  factory ProfileModel.fromParticipantModel(ParticipantModel participantModel) =>
      ProfileModel.fromJson(participantModel.toJson());

  factory ProfileModel.fromJson(Map<String, dynamic> json) => _$ProfileModelFromJson(json);
}
