enum ImageType {
  logo(storageDirectory: 'logo'),
  banner(storageDirectory: 'banner'),
  homeScreenGallery(storageDirectory: 'homeScreenGallery'),
  reusableEventPrimary(storageDirectory: 'reusableEventPrimary'),
  eventPrimary(storageDirectory: 'eventPrimary'),
  eventGallery(storageDirectory: 'eventGallery'),
  map(storageDirectory: 'maps'),
  participantPhoto(storageDirectory: 'participantPhotos'),
  generalContentPrimary(storageDirectory: 'generalContentPrimary'),
  generalContentTiles(storageDirectory: 'generalContentTiles'),
  ad(storageDirectory: 'imageAds');

  final String storageDirectory;

  const ImageType({required this.storageDirectory});
}
